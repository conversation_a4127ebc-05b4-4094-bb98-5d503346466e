<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\CyclistInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Cyclist\Command\CreateCyclistCommand;
use App\Domain\Cycling\Cyclist\Command\UpdateCyclistCommand;
use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Team\Team;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;

/**
 * @extends AbstractAdmin<Cyclist>
 *
 * @implements LifecycleMiddlewareInterface<Cyclist, CyclistInput>
 *
 * @method Cyclist getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_cyclist',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Cyclist::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class CyclistAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Cyclist, CyclistInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_cyclist';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'cyclist';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('firstName')
            ->add('lastName')
            ->add('country');

        if (!$this->isChild()) {
            $list->add('team');
        }

        $list
            ->add('inactiveAt')
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('firstName')
            ->add('lastName')
            ->add('country')
            ->add('inactiveAt', DateTimeType::class, [
                'widget' => 'single_text',
                'required' => false,
            ]);

        if (!$isChild) {
            $form->add('team', EntityType::class, [
                'class' => Team::class,
                'choice_label' => 'name',
            ]);
        }

        $form->end();
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateCyclistCommand(
            $submittedData->firstName,
            $submittedData->lastName,
            $submittedData->country,
            $this->getTeamId($submittedData),
            $submittedData->inactiveAt,
        );
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return CyclistInput::fromEntity($entity);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Cyclist
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $cyclist = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateCyclistCommand(
            $cyclist->getId(),
            $submittedData->firstName,
            $submittedData->lastName,
            $submittedData->country,
            $this->getTeamId($submittedData),
            $submittedData->inactiveAt,
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return CyclistInput::class;
    }

    private function getTeamId(object $submittedData): int
    {
        if ($this->isChild()) {
            $team = $this->getParent()->getSubject();
            if ($team instanceof Team) {
                return $team->getId();
            }
        }

        return $submittedData->team->getId();
    }
}

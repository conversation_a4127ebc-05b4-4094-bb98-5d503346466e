<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\TournamentInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Tournament\Command\CreateTournamentCommand;
use App\Domain\Cycling\Tournament\Command\UpdateTournamentCommand;
use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\Website\Website;
use Knp\Menu\ItemInterface;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Admin\AdminInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;

/**
 * @extends AbstractAdmin<Tournament>
 *
 * @implements LifecycleMiddlewareInterface<Tournament, TournamentInput>
 *
 * @method Tournament getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_tournament',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Tournament::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\Component\Messenger\MessageBusInterface']],
        ['addChild', ['@App\Application\Admin\Admin\Cycling\StageAdmin', 'tournament']],
        ['addChild', ['@App\Application\Admin\Admin\Cycling\TeamAdmin', 'tournament']],
    ],
)]
final class TournamentAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Tournament, TournamentInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_tournament';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'tournament';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('website')
            ->add('startsAt')
            ->add('endsAt')
            ->add('year', null, [
                'row_align' => 'left',
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with('global.general', ['class' => 'col-md-6'])
                ->add('name')
                ->add('website', EntityType::class, [
                    'class' => Website::class,
                ])
                ->add('startsAt', DateTimeType::class)
                ->add('endsAt', DateTimeType::class)
                ->add('registrationStartsAt', DateTimeType::class, [
                    'required' => false,
                ])
                ->add('registrationEndsAt', DateTimeType::class, [
                    'required' => false,
                ])
                ->add('year')
                ->add('externalId')
                ->add('nrOfCyclists')
                ->add('nrOfReserves')
            ->end();
    }

    protected function configureTabMenu(ItemInterface $menu, string $action, ?AdminInterface $childAdmin = null): void
    {
        if (!$childAdmin && !\in_array($action, ['edit', 'show'], true)) {
            return;
        }

        $admin = $this->isChild() ? $this->getParent() : $this;
        $id = $admin->getRequest()->get('id');

        /** @var Tournament $subject */
        $subject = $admin->getSubject();

        $menu->addChild($subject->getName(), $admin->generateMenuUrl('edit', ['id' => $id]));
        $menu->addChild(\sprintf('Etappes (%d)', $subject->getStages()->count()), $admin->generateMenuUrl('App\Application\Admin\Admin\Cycling\StageAdmin.list', ['id' => $id]));
        $menu->addChild(\sprintf('Teams (%d)', $subject->getTeams()->count()), $admin->generateMenuUrl('App\Application\Admin\Admin\Cycling\TeamAdmin.list', ['id' => $id]));
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    /**
     * @param TournamentInput $submittedData
     */
    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateTournamentCommand(
            $submittedData->name,
            $submittedData->startsAt,
            $submittedData->endsAt,
            $submittedData->registrationStartsAt,
            $submittedData->registrationEndsAt,
            $submittedData->year,
            $submittedData->externalId,
            $submittedData->nrOfCyclists,
            $submittedData->nrOfReserves,
            $submittedData->website->getId()
        );
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return TournamentInput::createFromEntity($entity);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Tournament
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    /**
     * @param TournamentInput $submittedData
     */
    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $tournament = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateTournamentCommand(
            $tournament->getId(),
            $submittedData->name,
            $submittedData->startsAt,
            $submittedData->endsAt,
            $submittedData->registrationStartsAt,
            $submittedData->registrationEndsAt,
            $submittedData->year,
            $submittedData->externalId,
            $submittedData->nrOfCyclists,
            $submittedData->nrOfReserves,
            $submittedData->website->getId()
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return TournamentInput::class;
    }
}

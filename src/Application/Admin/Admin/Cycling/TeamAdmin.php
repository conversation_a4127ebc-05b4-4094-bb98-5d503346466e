<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\TeamInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Team\Command\CreateTeamCommand;
use App\Domain\Cycling\Team\Command\UpdateTeamCommand;
use App\Domain\Cycling\Team\Team;
use App\Domain\Cycling\Tournament\Tournament;
use Knp\Menu\ItemInterface as MenuItemInterface;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Admin\AdminInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

/**
 * @extends AbstractAdmin<Team>
 *
 * @implements LifecycleMiddlewareInterface<Team, TeamInput>
 *
 * @method Team getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_team',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Team::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
        ['addChild', ['@App\\Application\\Admin\\Admin\\Cycling\\CyclistAdmin', 'team']],
    ],
)]
final class TeamAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Team, TeamInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_team';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'team';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('abbreviation')
            ->add('country')
            ->add('tournament')
            ->add('externalId');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('name')
            ->add('abbreviation')
            ->add('country')
            ->add('imageUrl')
            ->add('externalId')
            ->add('tournament', EntityType::class, [
                'class' => Tournament::class,
                'disabled' => $isChild,
                'choice_label' => 'name',
            ])
            ->end();

        if ($isChild) {
            $form->get('tournament')->setData($this->getParent()->getSubject());
        }
    }

    protected function configureTabMenu(MenuItemInterface $menu, string $action, ?AdminInterface $childAdmin = null): void
    {
        if (!$childAdmin) {
            return;
        }

        $admin = $this->isChild() ? $this->getParent() : $this;
        $id = $admin->getRequest()->get('id');

        /** @var Team $subject */
        $subject = $admin->getSubject();

        $menu->addChild($subject->getName(), $admin->generateMenuUrl('edit', ['id' => $id]));
        $menu->addChild(\sprintf('Renners (%d)', $subject->getCyclists()->count()), $admin->generateMenuUrl('App\Application\Admin\Admin\Cycling\CyclistAdmin.list', ['id' => $id]));
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateTeamCommand(
            $submittedData->name,
            $submittedData->abbreviation,
            $submittedData->country,
            $this->getTournamentId($submittedData),
            $submittedData->imageUrl,
            $submittedData->externalId,
        );
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return TeamInput::fromEntity($entity);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Team
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $team = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateTeamCommand(
            $team->getId(),
            $submittedData->name,
            $submittedData->abbreviation,
            $submittedData->country,
            $this->getTournamentId($submittedData),
            $submittedData->imageUrl,
            $submittedData->externalId,
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return TeamInput::class;
    }

    private function getTournamentId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $tournament = $this->getParent()->getSubject();
            if ($tournament instanceof Tournament) {
                return $tournament->getId();
            }
        }

        return $submittedData->tournament?->getId() ?? null;
    }
}

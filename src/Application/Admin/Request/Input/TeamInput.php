<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Team\Team;
use App\Domain\Cycling\Tournament\Tournament;
use Symfony\Component\Validator\Constraints as Assert;

final class TeamInput
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $name;

    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 3)]
    public string $abbreviation;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $country;

    #[Assert\Url]
    #[Assert\Length(max: 255)]
    public ?string $imageUrl = null;

    #[Assert\Length(max: 255)]
    #[Assert\Type(type: 'integer')]
    public ?int $externalId = null;

    #[Assert\NotBlank]
    public ?Tournament $tournament = null;

    public static function fromEntity(Team $team): self
    {
        $input = new self();
        $input->name = $team->getName();
        $input->abbreviation = $team->getAbbreviation();
        $input->country = $team->getCountry();
        $input->imageUrl = $team->getImageUrl();
        $input->externalId = $team->getExternalId();
        $input->tournament = $team->getTournament();

        return $input;
    }
}

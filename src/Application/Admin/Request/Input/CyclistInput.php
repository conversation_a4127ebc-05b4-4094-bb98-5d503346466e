<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Team\Team;
use DateTimeImmutable;
use Symfony\Component\Validator\Constraints as Assert;

final class CyclistInput
{
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $firstName;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $lastName;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public string $country;

    #[Assert\NotBlank]
    public ?Team $team = null;

    public ?DateTimeImmutable $inactiveAt = null;

    public static function fromEntity(Cyclist $cyclist): self
    {
        $input = new self();
        $input->firstName = $cyclist->getFirstName();
        $input->lastName = $cyclist->getLastName();
        $input->country = $cyclist->getCountry();
        $input->team = $cyclist->getTeam();
        $input->inactiveAt = $cyclist->getInactiveAt();

        return $input;
    }
}
